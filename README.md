# JobPortal Backend API

A comprehensive job portal API built with Node.js, Express, MongoDB, and JWT authentication.

## 🚀 Features

- **User Authentication**: Register, login, WeChat authentication
- **Profile Management**: Update user profiles with skills, experience, etc.
- **Job Management**: Create, read, update job postings
- **Role-based Access**: Different permissions for job seekers, employers, and admins
- **Advanced Search**: Filter jobs by location, type, salary, skills
- **Pagination**: Efficient data loading with pagination
- **Input Validation**: Comprehensive validation using express-validator
- **API Documentation**: Complete Swagger/OpenAPI documentation

## 📚 API Documentation

Once the server is running, visit:
- **Swagger UI**: `http://localhost:5000/api-docs`
- **Health Check**: `http://localhost:5000/health`

## 🛠️ Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd JobportalBackend
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Start MongoDB**
   Make sure MongoDB is running on your system

5. **Run the application**
   ```bash
   # Development mode
   npm run dev

   # Production mode
   npm start
   ```

## 🔧 Environment Variables

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# Database Configuration
MONGODB_URI=mongodb://localhost:27017/jobportal

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_EXPIRE=7d

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
```

## 📋 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/wechat` - WeChat authentication
- `GET /api/auth/profile` - Get current user profile
- `PUT /api/auth/profile` - Update user profile

### Jobs
- `POST /api/jobs` - Create job posting (Employer only)
- `GET /api/jobs` - Get all jobs with filtering
- `GET /api/jobs/my-jobs` - Get employer's jobs (Employer only)
- `GET /api/jobs/:id` - Get single job by ID
- `PUT /api/jobs/:id` - Update job posting (Owner only)

### System
- `GET /health` - Health check endpoint

## 🔐 Authentication

All endpoints (except register, login, wechat, and health) require JWT authentication.

**Header Format:**
```
Authorization: Bearer <your-jwt-token>
```

## 👥 User Roles

- **jobseeker**: Can view jobs, update own profile
- **employer**: Can create/edit jobs, view all jobs, update own profile
- **admin**: Full access to all endpoints

## 📝 Example Usage

### Register a new employer
```bash
POST /api/auth/register
Content-Type: application/json

{
  "userType": "employer",
  "name": "John Doe",
  "email": "<EMAIL>",
  "phone": "**********",
  "password": "Password123"
}
```

### Create a job posting
```bash
POST /api/jobs
Authorization: Bearer <jwt-token>
Content-Type: application/json

{
  "jobTitle": "Senior Full Stack Developer",
  "jobDescription": "We are looking for an experienced developer...",
  "requiredSkills": ["JavaScript", "React", "Node.js", "MongoDB"],
  "location": "San Francisco, CA",
  "jobType": "FullTime",
  "salaryRange": {
    "min": 80000,
    "max": 120000,
    "currency": "USD"
  },
  "experienceLevel": "Senior",
  "companyName": "Tech Corp"
}
```

### Search jobs
```bash
GET /api/jobs?search=developer&jobType=FullTime&location=San Francisco&page=1&limit=10
Authorization: Bearer <jwt-token>
```

## 🗄️ Database Schema

### User Schema
- userType: jobseeker | employer | admin
- name, email, phone, password
- profile: avatar, location, skills, experience, etc.
- timestamps

### Job Schema
- jobTitle, jobDescription, requiredSkills
- location, jobType, salaryRange
- employer (reference to User)
- experienceLevel, applicationDeadline
- isActive, viewsCount, applicationsCount
- timestamps

## 🔍 Search & Filtering

The API supports advanced filtering:
- **Text Search**: Search in job title, description, and skills
- **Location**: Filter by location (case-insensitive)
- **Job Type**: FullTime, PartTime, Contract, Internship, Remote
- **Experience Level**: Entry, Mid, Senior, Executive
- **Salary Range**: Filter by minimum and maximum salary
- **Pagination**: page and limit parameters

## 🛡️ Security Features

- **JWT Authentication**: Secure token-based authentication
- **Password Hashing**: bcryptjs with salt rounds
- **Input Validation**: Comprehensive validation on all endpoints
- **Role-based Authorization**: Different access levels
- **CORS Support**: Configurable cross-origin resource sharing
- **Error Handling**: Consistent error responses

## 🚦 Status Codes

- `200` - Success
- `201` - Created
- `400` - Bad Request (Validation Error)
- `401` - Unauthorized (Authentication Required)
- `403` - Forbidden (Insufficient Permissions)
- `404` - Not Found
- `409` - Conflict (Duplicate Data)
- `500` - Internal Server Error

## 📊 Response Format

All API responses follow this format:

```json
{
  "success": true,
  "message": "Operation successful",
  "data": {
    // Response data here
  }
}
```

Error responses:
```json
{
  "success": false,
  "message": "Error description",
  "errors": [
    {
      "field": "fieldName",
      "message": "Validation error message"
    }
  ]
}
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the ISC License.
