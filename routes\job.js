const express = require("express");
const { body, param, query } = require("express-validator");
const {
  createJob,
  getAllJobs,
  getJobById,
  getMyJobs,
  updateJob,
} = require("../controllers/jobController");
const {
  authenticateToken,
  authorizeRoles,
} = require("../middleware/authMiddleware");

const router = express.Router();

// Validation rules for job creation
const createJobValidation = [
  body("jobTitle")
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage("Job title must be between 3 and 100 characters"),
  body("jobDescription")
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage("Job description must be between 10 and 2000 characters"),
  body("requiredSkills")
    .isArray({ min: 1 })
    .withMessage("At least one required skill must be provided")
    .custom((skills) => {
      if (
        skills.some(
          (skill) => typeof skill !== "string" || skill.trim().length === 0
        )
      ) {
        throw new Error("All skills must be non-empty strings");
      }
      return true;
    }),
  body("location")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Location must be between 2 and 100 characters"),
  body("jobType")
    .isIn(["FullTime", "PartTime", "Contract", "Internship", "Remote"])
    .withMessage(
      "Job type must be FullTime, PartTime, Contract, Internship, or Remote"
    ),
  body("salaryRange.min")
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage("Minimum salary must be a positive number"),
  body("salaryRange.max")
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage("Maximum salary must be a positive number")
    .custom((value, { req }) => {
      if (value < req.body.salaryRange.min) {
        throw new Error(
          "Maximum salary must be greater than or equal to minimum salary"
        );
      }
      return true;
    }),
  body("salaryRange.currency")
    .optional()
    .isIn(["USD", "EUR", "GBP", "INR", "CNY"])
    .withMessage("Currency must be USD, EUR, GBP, INR, or CNY"),
  body("companyName")
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage("Company name cannot exceed 100 characters"),
  body("experienceLevel")
    .optional()
    .isIn(["Entry", "Mid", "Senior", "Executive"])
    .withMessage("Experience level must be Entry, Mid, Senior, or Executive"),
  body("applicationDeadline")
    .optional()
    .isISO8601()
    .withMessage("Application deadline must be a valid date")
    .custom((value) => {
      if (new Date(value) <= new Date()) {
        throw new Error("Application deadline must be in the future");
      }
      return true;
    }),
];

// Validation rules for job update (all fields optional)
const updateJobValidation = [
  body("jobTitle")
    .optional()
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage("Job title must be between 3 and 100 characters"),
  body("jobDescription")
    .optional()
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage("Job description must be between 10 and 2000 characters"),
  body("requiredSkills")
    .optional()
    .isArray({ min: 1 })
    .withMessage("At least one required skill must be provided")
    .custom((skills) => {
      if (
        skills.some(
          (skill) => typeof skill !== "string" || skill.trim().length === 0
        )
      ) {
        throw new Error("All skills must be non-empty strings");
      }
      return true;
    }),
  body("location")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Location must be between 2 and 100 characters"),
  body("jobType")
    .optional()
    .isIn(["FullTime", "PartTime", "Contract", "Internship", "Remote"])
    .withMessage(
      "Job type must be FullTime, PartTime, Contract, Internship, or Remote"
    ),
  body("salaryRange.min")
    .optional()
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage("Minimum salary must be a positive number"),
  body("salaryRange.max")
    .optional()
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage("Maximum salary must be a positive number")
    .custom((value, { req }) => {
      if (
        req.body.salaryRange &&
        req.body.salaryRange.min &&
        value < req.body.salaryRange.min
      ) {
        throw new Error(
          "Maximum salary must be greater than or equal to minimum salary"
        );
      }
      return true;
    }),
  body("salaryRange.currency")
    .optional()
    .isIn(["USD", "EUR", "GBP", "INR", "CNY"])
    .withMessage("Currency must be USD, EUR, GBP, INR, or CNY"),
  body("companyName")
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage("Company name cannot exceed 100 characters"),
  body("experienceLevel")
    .optional()
    .isIn(["Entry", "Mid", "Senior", "Executive"])
    .withMessage("Experience level must be Entry, Mid, Senior, or Executive"),
  body("applicationDeadline")
    .optional()
    .isISO8601()
    .withMessage("Application deadline must be a valid date")
    .custom((value) => {
      if (new Date(value) <= new Date()) {
        throw new Error("Application deadline must be in the future");
      }
      return true;
    }),
  body("isActive")
    .optional()
    .isBoolean()
    .withMessage("isActive must be a boolean value"),
];

// Validation for job ID parameter
const jobIdValidation = [
  param("id").isMongoId().withMessage("Invalid job ID format"),
];

// Validation for query parameters
const queryValidation = [
  query("page")
    .optional()
    .isInt({ min: 1 })
    .withMessage("Page must be a positive integer"),
  query("limit")
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage("Limit must be between 1 and 50"),
  query("minSalary")
    .optional()
    .isNumeric()
    .withMessage("Minimum salary must be a number"),
  query("maxSalary")
    .optional()
    .isNumeric()
    .withMessage("Maximum salary must be a number"),
  query("jobType")
    .optional()
    .isIn(["FullTime", "PartTime", "Contract", "Internship", "Remote"])
    .withMessage("Invalid job type"),
  query("experienceLevel")
    .optional()
    .isIn(["Entry", "Mid", "Senior", "Executive"])
    .withMessage("Invalid experience level"),
];

// Routes

/**
 * @swagger
 * /api/jobs:
 *   post:
 *     summary: Create a new job posting
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - jobTitle
 *               - jobDescription
 *               - requiredSkills
 *               - location
 *               - jobType
 *               - salaryRange
 *             properties:
 *               jobTitle:
 *                 type: string
 *                 minLength: 3
 *                 maxLength: 100
 *                 description: Job title
 *                 example: "Senior Full Stack Developer"
 *               jobDescription:
 *                 type: string
 *                 minLength: 10
 *                 maxLength: 2000
 *                 description: Detailed job description
 *                 example: "We are looking for an experienced full stack developer..."
 *               requiredSkills:
 *                 type: array
 *                 minItems: 1
 *                 items:
 *                   type: string
 *                 description: Required skills for the job
 *                 example: ["JavaScript", "React", "Node.js", "MongoDB"]
 *               location:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 description: Job location
 *                 example: "San Francisco, CA"
 *               jobType:
 *                 type: string
 *                 enum: [FullTime, PartTime, Contract, Internship, Remote]
 *                 description: Type of employment
 *                 example: "FullTime"
 *               salaryRange:
 *                 type: object
 *                 required:
 *                   - min
 *                   - max
 *                 properties:
 *                   min:
 *                     type: number
 *                     minimum: 0
 *                     description: Minimum salary
 *                     example: 80000
 *                   max:
 *                     type: number
 *                     minimum: 0
 *                     description: Maximum salary
 *                     example: 120000
 *                   currency:
 *                     type: string
 *                     enum: [USD, EUR, GBP, INR, CNY]
 *                     description: Currency code
 *                     example: "USD"
 *               companyName:
 *                 type: string
 *                 maxLength: 100
 *                 description: Company name
 *                 example: "Tech Corp"
 *               experienceLevel:
 *                 type: string
 *                 enum: [Entry, Mid, Senior, Executive]
 *                 description: Required experience level
 *                 example: "Senior"
 *               applicationDeadline:
 *                 type: string
 *                 format: date-time
 *                 description: Application deadline
 *                 example: "2024-12-31T23:59:59.000Z"
 *     responses:
 *       201:
 *         description: Job posted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Job posted successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     job:
 *                       $ref: '#/components/schemas/Job'
 *       400:
 *         description: Validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Access denied - employer role required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post(
  "/",
  authenticateToken,
  authorizeRoles("employer", "admin"),
  createJobValidation,
  createJob
);

/**
 * @swagger
 * /api/jobs:
 *   get:
 *     summary: Get all active jobs with filtering and pagination
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Number of jobs per page
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Text search in job title, description, and skills
 *     responses:
 *       200:
 *         description: Jobs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/", authenticateToken, queryValidation, getAllJobs);

/**
 * @swagger
 * /api/jobs/my-jobs:
 *   get:
 *     summary: Get employer's posted jobs
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of jobs per page
 *     responses:
 *       200:
 *         description: Employer jobs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied - employer role required
 */
router.get(
  "/my-jobs",
  authenticateToken,
  authorizeRoles("employer", "admin"),
  queryValidation,
  getMyJobs
);

/**
 * @swagger
 * /api/jobs/{id}:
 *   get:
 *     summary: Get single job by ID
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Job ID
 *     responses:
 *       200:
 *         description: Job retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Invalid job ID
 *       404:
 *         description: Job not found
 *       401:
 *         description: Authentication required
 */
router.get("/:id", authenticateToken, jobIdValidation, getJobById);

/**
 * @swagger
 * /api/jobs/{id}:
 *   put:
 *     summary: Update job posting (Employer who owns the job only)
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Job ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               jobTitle:
 *                 type: string
 *                 example: "Senior Full Stack Developer - Updated"
 *               jobDescription:
 *                 type: string
 *                 example: "Updated job description..."
 *               requiredSkills:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["JavaScript", "React", "Node.js", "AWS"]
 *               location:
 *                 type: string
 *                 example: "San Francisco, CA (Remote OK)"
 *               jobType:
 *                 type: string
 *                 enum: [FullTime, PartTime, Contract, Internship, Remote]
 *                 example: "FullTime"
 *               salaryRange:
 *                 type: object
 *                 properties:
 *                   min:
 *                     type: number
 *                     example: 90000
 *                   max:
 *                     type: number
 *                     example: 130000
 *                   currency:
 *                     type: string
 *                     example: "USD"
 *               isActive:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Job updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Validation failed or invalid job ID
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied - can only edit own jobs
 *       404:
 *         description: Job not found
 */
router.put(
  "/:id",
  authenticateToken,
  authorizeRoles("employer", "admin"),
  jobIdValidation,
  updateJobValidation,
  updateJob
);

module.exports = router;
