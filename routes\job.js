const express = require("express");
const { body, param, query } = require("express-validator");
const {
  createJob,
  getAllJobs,
  getJobById,
  getMyJobs,
  updateJob,
} = require("../controllers/jobController");
const {
  authenticateToken,
  authorizeRoles,
} = require("../middleware/authMiddleware");

const router = express.Router();

// Validation rules for job creation
const createJobValidation = [
  body("jobTitle")
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage("Job title must be between 3 and 100 characters"),
  body("jobDescription")
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage("Job description must be between 10 and 2000 characters"),
  body("requiredSkills")
    .isArray({ min: 1 })
    .withMessage("At least one required skill must be provided")
    .custom((skills) => {
      if (
        skills.some(
          (skill) => typeof skill !== "string" || skill.trim().length === 0
        )
      ) {
        throw new Error("All skills must be non-empty strings");
      }
      return true;
    }),
  body("location")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Location must be between 2 and 100 characters"),
  body("jobType")
    .isIn(["FullTime", "PartTime", "Contract", "Internship", "Remote"])
    .withMessage(
      "Job type must be FullTime, PartTime, Contract, Internship, or Remote"
    ),
  body("salaryRange.min")
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage("Minimum salary must be a positive number"),
  body("salaryRange.max")
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage("Maximum salary must be a positive number")
    .custom((value, { req }) => {
      if (value < req.body.salaryRange.min) {
        throw new Error(
          "Maximum salary must be greater than or equal to minimum salary"
        );
      }
      return true;
    }),
  body("salaryRange.currency")
    .optional()
    .isIn(["USD", "EUR", "GBP", "INR", "CNY"])
    .withMessage("Currency must be USD, EUR, GBP, INR, or CNY"),
  body("companyName")
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage("Company name cannot exceed 100 characters"),
  body("experienceLevel")
    .optional()
    .isIn(["Entry", "Mid", "Senior", "Executive"])
    .withMessage("Experience level must be Entry, Mid, Senior, or Executive"),
  body("applicationDeadline")
    .optional()
    .isISO8601()
    .withMessage("Application deadline must be a valid date")
    .custom((value) => {
      if (new Date(value) <= new Date()) {
        throw new Error("Application deadline must be in the future");
      }
      return true;
    }),
];

// Validation rules for job update (all fields optional)
const updateJobValidation = [
  body("jobTitle")
    .optional()
    .trim()
    .isLength({ min: 3, max: 100 })
    .withMessage("Job title must be between 3 and 100 characters"),
  body("jobDescription")
    .optional()
    .trim()
    .isLength({ min: 10, max: 2000 })
    .withMessage("Job description must be between 10 and 2000 characters"),
  body("requiredSkills")
    .optional()
    .isArray({ min: 1 })
    .withMessage("At least one required skill must be provided")
    .custom((skills) => {
      if (
        skills.some(
          (skill) => typeof skill !== "string" || skill.trim().length === 0
        )
      ) {
        throw new Error("All skills must be non-empty strings");
      }
      return true;
    }),
  body("location")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Location must be between 2 and 100 characters"),
  body("jobType")
    .optional()
    .isIn(["FullTime", "PartTime", "Contract", "Internship", "Remote"])
    .withMessage(
      "Job type must be FullTime, PartTime, Contract, Internship, or Remote"
    ),
  body("salaryRange.min")
    .optional()
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage("Minimum salary must be a positive number"),
  body("salaryRange.max")
    .optional()
    .isNumeric()
    .isFloat({ min: 0 })
    .withMessage("Maximum salary must be a positive number")
    .custom((value, { req }) => {
      if (
        req.body.salaryRange &&
        req.body.salaryRange.min &&
        value < req.body.salaryRange.min
      ) {
        throw new Error(
          "Maximum salary must be greater than or equal to minimum salary"
        );
      }
      return true;
    }),
  body("salaryRange.currency")
    .optional()
    .isIn(["USD", "EUR", "GBP", "INR", "CNY"])
    .withMessage("Currency must be USD, EUR, GBP, INR, or CNY"),
  body("companyName")
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage("Company name cannot exceed 100 characters"),
  body("experienceLevel")
    .optional()
    .isIn(["Entry", "Mid", "Senior", "Executive"])
    .withMessage("Experience level must be Entry, Mid, Senior, or Executive"),
  body("applicationDeadline")
    .optional()
    .isISO8601()
    .withMessage("Application deadline must be a valid date")
    .custom((value) => {
      if (new Date(value) <= new Date()) {
        throw new Error("Application deadline must be in the future");
      }
      return true;
    }),
  body("isActive")
    .optional()
    .isBoolean()
    .withMessage("isActive must be a boolean value"),
];

// Validation for job ID parameter
const jobIdValidation = [
  param("id").isMongoId().withMessage("Invalid job ID format"),
];

// Validation for query parameters
const queryValidation = [
  query("page")
    .optional()
    .isInt({ min: 1 })
    .withMessage("Page must be a positive integer"),
  query("limit")
    .optional()
    .isInt({ min: 1, max: 50 })
    .withMessage("Limit must be between 1 and 50"),
  query("minSalary")
    .optional()
    .isNumeric()
    .withMessage("Minimum salary must be a number"),
  query("maxSalary")
    .optional()
    .isNumeric()
    .withMessage("Maximum salary must be a number"),
  query("jobType")
    .optional()
    .isIn(["FullTime", "PartTime", "Contract", "Internship", "Remote"])
    .withMessage("Invalid job type"),
  query("experienceLevel")
    .optional()
    .isIn(["Entry", "Mid", "Senior", "Executive"])
    .withMessage("Invalid experience level"),
];

// Routes

// POST /api/jobs - Create a new job (Employer only)
router.post(
  "/",
  authenticateToken,
  authorizeRoles("employer", "admin"),
  createJobValidation,
  createJob
);

// GET /api/jobs - Get all active jobs (All authenticated users)
router.get("/", authenticateToken, queryValidation, getAllJobs);

// GET /api/jobs/my-jobs - Get employer's posted jobs (Employer only)
router.get(
  "/my-jobs",
  authenticateToken,
  authorizeRoles("employer", "admin"),
  queryValidation,
  getMyJobs
);

// GET /api/jobs/:id - Get single job by ID (All authenticated users)
router.get("/:id", authenticateToken, jobIdValidation, getJobById);

// PUT /api/jobs/:id - Update job posting (Employer who owns the job only)
router.put(
  "/:id",
  authenticateToken,
  authorizeRoles("employer", "admin"),
  jobIdValidation,
  updateJobValidation,
  updateJob
);

module.exports = router;
