const jwt = require("jsonwebtoken");
const { validationResult } = require("express-validator");
const User = require("../model/user");

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE || "7d",
  });
};

// Register new user
const register = async (req, res) => {
  try {
    const { userType, name, email, phone, password } = req.body;
    if (!userType || !name || !email || !phone || !password) {
      return res.status(400).json({
        success: false,
        message: "All fields are required",
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { phone }],
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: "User already exists with this email or phone number",
      });
    }

    // Create new user
    const user = new User({
      userType,
      name,
      email,
      phone,
      password,
    });

    await user.save();

    // Generate token
    const token = generateToken(user._id);

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    res.status(201).json({
      success: true,
      message: "User registered successfully",
      data: {
        user,
        token,
      },
    });
  } catch (error) {
    console.error("Registration error:", error);

    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(409).json({
        success: false,
        message: `${field} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: "Server error during registration",
    });
  }
};

// Login user
const login = async (req, res) => {
  try {
    const { phone, password } = req.body;
    if (!phone || !password) {
      return res.status(400).json({
        success: false,
        message: "Phone number and password are required",
      });
    }

    // Find user by phone
    const user = await User.findOne({ phone }).select("+password");

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid phone number or password",
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: "Account is deactivated",
      });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);

    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid phone number or password",
      });
    }

    // Generate token
    const token = generateToken(user._id);

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Remove password from response
    user.password = undefined;

    res.json({
      success: true,
      message: "Login successful",
      data: {
        user,
        token,
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      success: false,
      message: "Server error during login",
    });
  }
};

// WeChat authentication
const wechatAuth = async (req, res) => {
  try {
    const { wechatToken } = req.body;

    // TODO: Verify WeChat token with WeChat API
    // This is a placeholder - you'll need to implement actual WeChat verification
    // const wechatUserInfo = await verifyWeChatToken(wechatToken);

    // For now, we'll simulate WeChat user info
    // In production, replace this with actual WeChat API call
    const wechatUserInfo = {
      openid: "simulated_openid_" + Date.now(),
      nickname: "WeChat User",
      headimgurl: "https://example.com/avatar.jpg",
    };

    if (!wechatUserInfo || !wechatUserInfo.openid) {
      return res.status(401).json({
        success: false,
        message: "Invalid WeChat token",
      });
    }

    // Check if user exists with this WeChat ID
    let user = await User.findOne({ wechatId: wechatUserInfo.openid });

    if (!user) {
      // Create new user with WeChat info
      user = new User({
        userType: "jobseeker", // Default type for WeChat users
        name: wechatUserInfo.nickname || "WeChat User",
        email: `wechat_${wechatUserInfo.openid}@temp.com`, // Temporary email
        phone: `wechat_${Date.now()}`, // Temporary phone - should be updated later
        wechatId: wechatUserInfo.openid,
        profile: {
          avatar: wechatUserInfo.headimgurl,
        },
      });

      await user.save();
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: "Account is deactivated",
      });
    }

    // Generate token
    const token = generateToken(user._id);

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    res.json({
      success: true,
      message: "WeChat authentication successful",
      data: {
        user,
        token,
        isNewUser: !user.phone.startsWith("wechat_"), // Check if user needs to complete profile
      },
    });
  } catch (error) {
    console.error("WeChat auth error:", error);
    res.status(500).json({
      success: false,
      message: "Server error during WeChat authentication",
    });
  }
};

// Get current user profile
const getProfile = async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        user: req.user,
      },
    });
  } catch (error) {
    console.error("Get profile error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while fetching profile",
    });
  }
};

// Update user profile
const updateProfile = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: errors.array(),
      });
    }

    const { name, email, phone, profile } = req.body;

    const userId = req.user._id;

    // Check if email or phone already exists for other users
    if (email || phone) {
      const existingUser = await User.findOne({
        _id: { $ne: userId },
        $or: [...(email ? [{ email }] : []), ...(phone ? [{ phone }] : [])],
      });

      if (existingUser) {
        return res.status(409).json({
          success: false,
          message: "Email or phone number already exists",
        });
      }
    }

    // Build update object
    const updateData = {};
    if (name !== undefined) updateData.name = name;
    if (email !== undefined) updateData.email = email;
    if (phone !== undefined) updateData.phone = phone;

    // Handle profile updates
    if (profile) {
      updateData.profile = {};
      if (profile.avatar !== undefined)
        updateData.profile.avatar = profile.avatar;
      if (profile.location !== undefined)
        updateData.profile.location = profile.location;
      if (profile.prefLanguage !== undefined)
        updateData.profile.prefLanguage = profile.prefLanguage;
      if (profile.experienceLevel !== undefined)
        updateData.profile.experienceLevel = profile.experienceLevel;
      if (profile.currentPosition !== undefined)
        updateData.profile.currentPosition = profile.currentPosition;
      if (profile.education !== undefined)
        updateData.profile.education = profile.education;
      if (profile.skills !== undefined)
        updateData.profile.skills = profile.skills;
    }

    // Update user
    const updatedUser = await User.findByIdAndUpdate(
      userId,
      { $set: updateData },
      {
        new: true,
        runValidators: true,
        select: "-password",
      }
    );

    if (!updatedUser) {
      return res.status(404).json({
        success: false,
        message: "User not found",
      });
    }

    res.json({
      success: true,
      message: "Profile updated successfully",
      data: {
        user: updatedUser,
      },
    });
  } catch (error) {
    console.error("Update profile error:", error);

    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(409).json({
        success: false,
        message: `${field} already exists`,
      });
    }

    if (error.name === "ValidationError") {
      const validationErrors = Object.values(error.errors).map((err) => ({
        field: err.path,
        message: err.message,
      }));

      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: validationErrors,
      });
    }

    res.status(500).json({
      success: false,
      message: "Server error while updating profile",
    });
  }
};

module.exports = {
  register,
  login,
  wechatAuth,
  getProfile,
  updateProfile,
};
