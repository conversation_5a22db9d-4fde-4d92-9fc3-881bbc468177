const jwt = require("jsonwebtoken");
const { validationResult } = require("express-validator");
const User = require("../model/user");

// Generate JWT token
const generateToken = (userId) => {
  return jwt.sign({ userId }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRE || "7d",
  });
};

// Register new user
const register = async (req, res) => {
  try {
    const { userType, name, email, phone, password } = req.body;
    if (!userType || !name || !email || !phone || !password) {
      return res.status(400).json({
        success: false,
        message: "All fields are required",
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({
      $or: [{ email }, { phone }],
    });

    if (existingUser) {
      return res.status(409).json({
        success: false,
        message: "User already exists with this email or phone number",
      });
    }

    // Create new user
    const user = new User({
      userType,
      name,
      email,
      phone,
      password,
    });

    await user.save();

    // Generate token
    const token = generateToken(user._id);

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    res.status(201).json({
      success: true,
      message: "User registered successfully",
      data: {
        user,
        token,
      },
    });
  } catch (error) {
    console.error("Registration error:", error);

    if (error.code === 11000) {
      const field = Object.keys(error.keyPattern)[0];
      return res.status(409).json({
        success: false,
        message: `${field} already exists`,
      });
    }

    res.status(500).json({
      success: false,
      message: "Server error during registration",
    });
  }
};

// Login user
const login = async (req, res) => {
  try {
    const { phone, password } = req.body;
    if (!phone || !password) {
      return res.status(400).json({
        success: false,
        message: "Phone number and password are required",
      });
    }

    // Find user by phone
    const user = await User.findOne({ phone }).select("+password");

    if (!user) {
      return res.status(401).json({
        success: false,
        message: "Invalid phone number or password",
      });
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: "Account is deactivated",
      });
    }

    // Check password
    const isPasswordValid = await user.comparePassword(password);

    if (!isPasswordValid) {
      return res.status(401).json({
        success: false,
        message: "Invalid phone number or password",
      });
    }

    // Generate token
    const token = generateToken(user._id);

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    // Remove password from response
    user.password = undefined;

    res.json({
      success: true,
      message: "Login successful",
      data: {
        user,
        token,
      },
    });
  } catch (error) {
    console.error("Login error:", error);
    res.status(500).json({
      success: false,
      message: "Server error during login",
    });
  }
};

// WeChat authentication
const wechatAuth = async (req, res) => {
  try {
    const { wechatToken } = req.body;

    // TODO: Verify WeChat token with WeChat API
    // This is a placeholder - you'll need to implement actual WeChat verification
    // const wechatUserInfo = await verifyWeChatToken(wechatToken);

    // For now, we'll simulate WeChat user info
    // In production, replace this with actual WeChat API call
    const wechatUserInfo = {
      openid: "simulated_openid_" + Date.now(),
      nickname: "WeChat User",
      headimgurl: "https://example.com/avatar.jpg",
    };

    if (!wechatUserInfo || !wechatUserInfo.openid) {
      return res.status(401).json({
        success: false,
        message: "Invalid WeChat token",
      });
    }

    // Check if user exists with this WeChat ID
    let user = await User.findOne({ wechatId: wechatUserInfo.openid });

    if (!user) {
      // Create new user with WeChat info
      user = new User({
        userType: "jobseeker", // Default type for WeChat users
        name: wechatUserInfo.nickname || "WeChat User",
        email: `wechat_${wechatUserInfo.openid}@temp.com`, // Temporary email
        phone: `wechat_${Date.now()}`, // Temporary phone - should be updated later
        wechatId: wechatUserInfo.openid,
        profile: {
          avatar: wechatUserInfo.headimgurl,
        },
      });

      await user.save();
    }

    // Check if account is active
    if (!user.isActive) {
      return res.status(401).json({
        success: false,
        message: "Account is deactivated",
      });
    }

    // Generate token
    const token = generateToken(user._id);

    // Update last login
    user.lastLogin = new Date();
    await user.save();

    res.json({
      success: true,
      message: "WeChat authentication successful",
      data: {
        user,
        token,
        isNewUser: !user.phone.startsWith("wechat_"), // Check if user needs to complete profile
      },
    });
  } catch (error) {
    console.error("WeChat auth error:", error);
    res.status(500).json({
      success: false,
      message: "Server error during WeChat authentication",
    });
  }
};

// Get current user profile
const getProfile = async (req, res) => {
  try {
    res.json({
      success: true,
      data: {
        user: req.user,
      },
    });
  } catch (error) {
    console.error("Get profile error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while fetching profile",
    });
  }
};

module.exports = {
  register,
  login,
  wechatAuth,
  getProfile,
};
