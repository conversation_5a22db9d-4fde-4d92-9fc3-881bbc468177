{"name": "jobportalbackend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "git+https://github.com/umerhere23/JobportalBackend.git"}, "author": "", "license": "ISC", "bugs": {"url": "https://github.com/umerhere23/JobportalBackend/issues"}, "homepage": "https://github.com/umerhere23/JobportalBackend#readme", "description": "", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "express-validator": "^7.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}}