const mongoose = require("mongoose");

const jobSchema = new mongoose.Schema(
  {
    jobTitle: {
      type: String,

      trim: true,
      maxlength: [100, "Job title cannot exceed 100 characters"],
    },
    jobDescription: {
      type: String,

      trim: true,
      maxlength: [2000, "Job description cannot exceed 2000 characters"],
    },
    requiredSkills: [
      {
        type: String,
        trim: true,
        maxlength: [50, "Each skill cannot exceed 50 characters"],
      },
    ],
    location: {
      type: String,

      trim: true,
      maxlength: [100, "Location cannot exceed 100 characters"],
    },
    jobType: {
      type: String,

      enum: {
        values: ["FullTime", "PartTime", "Contract", "Internship", "Remote"],
        message:
          "Job type must be FullTime, PartTime, Contract, Internship, or Remote",
      },
    },
    salaryRange: {
      min: {
        type: Number,

        min: [0, "Minimum salary cannot be negative"],
      },
      max: {
        type: Number,

        min: [0, "Maximum salary cannot be negative"],
        validate: {
          validator: function (value) {
            return value >= this.salaryRange.min;
          },
          message:
            "Maximum salary must be greater than or equal to minimum salary",
        },
      },
      currency: {
        type: String,
        default: "USD",
        enum: ["USD", "EUR", "GBP", "INR", "CNY"],
      },
    },
    employer: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
    },
    companyName: {
      type: String,
      trim: true,
      maxlength: [100, "Company name cannot exceed 100 characters"],
    },
    experienceLevel: {
      type: String,
      enum: ["Entry", "Mid", "Senior", "Executive"],
      default: "Entry",
    },
    applicationDeadline: {
      type: Date,
      validate: {
        validator: function (value) {
          return value > new Date();
        },
        message: "Application deadline must be in the future",
      },
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    applicationsCount: {
      type: Number,
      default: 0,
      min: 0,
    },
    viewsCount: {
      type: Number,
      default: 0,
      min: 0,
    },
  },
  {
    timestamps: true,
  }
);

// Index for better search performance
jobSchema.index({
  jobTitle: "text",
  jobDescription: "text",
  requiredSkills: "text",
});
jobSchema.index({ location: 1 });
jobSchema.index({ jobType: 1 });
jobSchema.index({ employer: 1 });
jobSchema.index({ isActive: 1 });
jobSchema.index({ createdAt: -1 });

// Virtual for formatted salary range
jobSchema.virtual("formattedSalaryRange").get(function () {
  return `${
    this.salaryRange.currency
  } ${this.salaryRange.min.toLocaleString()} - ${this.salaryRange.max.toLocaleString()}`;
});

// Method to increment views
jobSchema.methods.incrementViews = function () {
  this.viewsCount += 1;
  return this.save();
};

// Static method to find active jobs
jobSchema.statics.findActiveJobs = function (filter = {}) {
  return this.find({ ...filter, isActive: true })
    .populate("employer", "name email profile.location")
    .sort({ createdAt: -1 });
};

module.exports = mongoose.model("Job", jobSchema);
