// Additional Swagger documentation for auth and job endpoints

/**
 * @swagger
 * tags:
 *   - name: Authentication
 *     description: User authentication and profile management
 *   - name: Jobs
 *     description: Job posting and management
 */

/**
 * @swagger
 * /api/auth/wechat:
 *   post:
 *     summary: WeChat authentication
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - wechatToken
 *             properties:
 *               wechatToken:
 *                 type: string
 *                 description: WeChat authentication token
 *                 example: "wechat_token_here"
 *     responses:
 *       200:
 *         description: WeChat authentication successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: WeChat authentication successful
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *                     token:
 *                       type: string
 *                       description: JWT authentication token
 *                     isNewUser:
 *                       type: boolean
 *                       description: Whether this is a new user
 *       400:
 *         description: Validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Invalid WeChat token or account deactivated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * @swagger
 * /api/auth/profile:
 *   get:
 *     summary: Get current user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *       401:
 *         description: Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *   put:
 *     summary: Update user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 description: Full name
 *                 example: John Updated
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address
 *                 example: <EMAIL>
 *               phone:
 *                 type: string
 *                 pattern: '^[0-9]{10,15}$'
 *                 description: Phone number
 *                 example: "9876543210"
 *               profile:
 *                 type: object
 *                 properties:
 *                   avatar:
 *                     type: string
 *                     format: uri
 *                     description: Profile picture URL
 *                     example: "https://example.com/avatar.jpg"
 *                   location:
 *                     type: string
 *                     maxLength: 100
 *                     description: User location
 *                     example: "New York, NY"
 *                   prefLanguage:
 *                     type: string
 *                     maxLength: 50
 *                     description: Preferred language
 *                     example: "English"
 *                   experienceLevel:
 *                     type: string
 *                     enum: [Entry, Mid, Senior, Executive]
 *                     description: Experience level
 *                     example: "Senior"
 *                   currentPosition:
 *                     type: string
 *                     maxLength: 100
 *                     description: Current job position
 *                     example: "Senior Developer"
 *                   education:
 *                     type: string
 *                     maxLength: 200
 *                     description: Educational background
 *                     example: "Computer Science, MIT"
 *                   skills:
 *                     type: array
 *                     maxItems: 20
 *                     items:
 *                       type: string
 *                     description: List of skills
 *                     example: ["JavaScript", "React", "Node.js"]
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Profile updated successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *       400:
 *         description: Validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       409:
 *         description: Email or phone already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * @swagger
 * /api/jobs:
 *   get:
 *     summary: Get all active jobs with filtering and pagination
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Number of jobs per page
 *       - in: query
 *         name: jobType
 *         schema:
 *           type: string
 *           enum: [FullTime, PartTime, Contract, Internship, Remote]
 *         description: Filter by job type
 *       - in: query
 *         name: location
 *         schema:
 *           type: string
 *         description: Filter by location (case-insensitive)
 *       - in: query
 *         name: experienceLevel
 *         schema:
 *           type: string
 *           enum: [Entry, Mid, Senior, Executive]
 *         description: Filter by experience level
 *       - in: query
 *         name: minSalary
 *         schema:
 *           type: number
 *         description: Minimum salary filter
 *       - in: query
 *         name: maxSalary
 *         schema:
 *           type: number
 *         description: Maximum salary filter
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: Text search in job title, description, and skills
 *     responses:
 *       200:
 *         description: Jobs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     jobs:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Job'
 *                     pagination:
 *                       type: object
 *                       properties:
 *                         currentPage:
 *                           type: integer
 *                         totalPages:
 *                           type: integer
 *                         totalJobs:
 *                           type: integer
 *                         hasNextPage:
 *                           type: boolean
 *                         hasPrevPage:
 *                           type: boolean
 *       401:
 *         description: Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * @swagger
 * /api/jobs/my-jobs:
 *   get:
 *     summary: Get employer's posted jobs
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 50
 *           default: 10
 *         description: Number of jobs per page
 *       - in: query
 *         name: isActive
 *         schema:
 *           type: boolean
 *         description: Filter by active status
 *     responses:
 *       200:
 *         description: Employer jobs retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     jobs:
 *                       type: array
 *                       items:
 *                         $ref: '#/components/schemas/Job'
 *                     pagination:
 *                       type: object
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied - employer role required
 */

/**
 * @swagger
 * /api/jobs/{id}:
 *   get:
 *     summary: Get single job by ID
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Job ID
 *     responses:
 *       200:
 *         description: Job retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 data:
 *                   type: object
 *                   properties:
 *                     job:
 *                       $ref: '#/components/schemas/Job'
 *       400:
 *         description: Invalid job ID
 *       404:
 *         description: Job not found or inactive
 *       401:
 *         description: Authentication required
 *   put:
 *     summary: Update job posting (Employer who owns the job only)
 *     tags: [Jobs]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: string
 *         description: Job ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               jobTitle:
 *                 type: string
 *                 example: "Senior Full Stack Developer - Updated"
 *               jobDescription:
 *                 type: string
 *                 example: "Updated job description..."
 *               requiredSkills:
 *                 type: array
 *                 items:
 *                   type: string
 *                 example: ["JavaScript", "React", "Node.js", "AWS"]
 *               location:
 *                 type: string
 *                 example: "San Francisco, CA (Remote OK)"
 *               jobType:
 *                 type: string
 *                 enum: [FullTime, PartTime, Contract, Internship, Remote]
 *                 example: "FullTime"
 *               salaryRange:
 *                 type: object
 *                 properties:
 *                   min:
 *                     type: number
 *                     example: 90000
 *                   max:
 *                     type: number
 *                     example: 130000
 *                   currency:
 *                     type: string
 *                     example: "USD"
 *               isActive:
 *                 type: boolean
 *                 example: true
 *     responses:
 *       200:
 *         description: Job updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Validation failed or invalid job ID
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Access denied - can only edit own jobs
 *       404:
 *         description: Job not found
 */

/**
 * @swagger
 * /api/health:
 *   get:
 *     summary: Health check endpoint
 *     tags: [System]
 *     responses:
 *       200:
 *         description: Server is running
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Server is running
 *                 timestamp:
 *                   type: string
 *                   format: date-time
 *                   example: "2024-01-01T00:00:00.000Z"
 */

module.exports = {};
