const { validationResult } = require("express-validator");
const Job = require("../model/job");
const User = require("../model/user");

// Create a new job posting (Employer only)
const createJob = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: errors.array(),
      });
    }

    const {
      jobTitle,
      jobDescription,
      requiredSkills,
      location,
      jobType,
      salaryRange,
      companyName,
      experienceLevel,
      applicationDeadline,
    } = req.body;

    // Create new job
    const job = new Job({
      jobTitle,
      jobDescription,
      requiredSkills: Array.isArray(requiredSkills)
        ? requiredSkills
        : [requiredSkills],
      location,
      jobType,
      salaryRange,
      employer: req.user._id,
      companyName: companyName || req.user.name,
      experienceLevel,
      applicationDeadline,
    });

    await job.save();

    // Populate employer details
    await job.populate("employer", "name email profile.location");

    res.status(201).json({
      success: true,
      message: "Job posted successfully",
      data: {
        job,
      },
    });
  } catch (error) {
    console.error("Create job error:", error);

    if (error.name === "ValidationError") {
      const validationErrors = Object.values(error.errors).map((err) => ({
        field: err.path,
        message: err.message,
      }));

      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: validationErrors,
      });
    }

    res.status(500).json({
      success: false,
      message: "Server error while creating job",
    });
  }
};

// Get all active jobs with filtering and pagination
const getAllJobs = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      jobType,
      location,
      experienceLevel,
      minSalary,
      maxSalary,
      search,
    } = req.query;

    // Build filter object
    const filter = { isActive: true };

    if (jobType) filter.jobType = jobType;
    if (location) filter.location = new RegExp(location, "i");
    if (experienceLevel) filter.experienceLevel = experienceLevel;

    if (minSalary || maxSalary) {
      filter["salaryRange.min"] = {};
      if (minSalary) filter["salaryRange.min"]["$gte"] = parseInt(minSalary);
      if (maxSalary) filter["salaryRange.max"] = { $lte: parseInt(maxSalary) };
    }

    // Text search
    if (search) {
      filter.$text = { $search: search };
    }

    // Calculate pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Get jobs with pagination
    const jobs = await Job.find(filter)
      .populate("employer", "name email profile.location")
      .sort(search ? { score: { $meta: "textScore" } } : { createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    // Get total count for pagination
    const totalJobs = await Job.countDocuments(filter);
    const totalPages = Math.ceil(totalJobs / limitNum);

    res.json({
      success: true,
      data: {
        jobs,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalJobs,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1,
        },
      },
    });
  } catch (error) {
    console.error("Get all jobs error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while fetching jobs",
    });
  }
};

// Get single job by ID
const getJobById = async (req, res) => {
  try {
    const { id } = req.params;

    const job = await Job.findById(id).populate(
      "employer",
      "name email profile.location profile.bio"
    );

    if (!job) {
      return res.status(404).json({
        success: false,
        message: "Job not found",
      });
    }

    if (!job.isActive) {
      return res.status(404).json({
        success: false,
        message: "Job is no longer active",
      });
    }

    // Increment view count (don't wait for it)
    job
      .incrementViews()
      .catch((err) => console.error("Error incrementing views:", err));

    res.json({
      success: true,
      data: {
        job,
      },
    });
  } catch (error) {
    console.error("Get job by ID error:", error);

    if (error.name === "CastError") {
      return res.status(400).json({
        success: false,
        message: "Invalid job ID",
      });
    }

    res.status(500).json({
      success: false,
      message: "Server error while fetching job",
    });
  }
};

// Get employer's posted jobs
const getMyJobs = async (req, res) => {
  try {
    const { page = 1, limit = 10, isActive } = req.query;

    // Build filter
    const filter = { employer: req.user._id };
    if (isActive !== undefined) {
      filter.isActive = isActive === "true";
    }

    // Calculate pagination
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const skip = (pageNum - 1) * limitNum;

    // Get jobs
    const jobs = await Job.find(filter)
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limitNum);

    // Get total count
    const totalJobs = await Job.countDocuments(filter);
    const totalPages = Math.ceil(totalJobs / limitNum);

    res.json({
      success: true,
      data: {
        jobs,
        pagination: {
          currentPage: pageNum,
          totalPages,
          totalJobs,
          hasNextPage: pageNum < totalPages,
          hasPrevPage: pageNum > 1,
        },
      },
    });
  } catch (error) {
    console.error("Get my jobs error:", error);
    res.status(500).json({
      success: false,
      message: "Server error while fetching your jobs",
    });
  }
};

// Update job posting (Employer only - must own the job)
const updateJob = async (req, res) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: errors.array(),
      });
    }

    const { id } = req.params;
    const {
      jobTitle,
      jobDescription,
      requiredSkills,
      location,
      jobType,
      salaryRange,
      companyName,
      experienceLevel,
      applicationDeadline,
      isActive,
    } = req.body;

    // Find the job first
    const job = await Job.findById(id);

    if (!job) {
      return res.status(404).json({
        success: false,
        message: "Job not found",
      });
    }

    // Check if the user owns this job
    if (job.employer.toString() !== req.user._id.toString()) {
      return res.status(403).json({
        success: false,
        message: "Access denied - you can only edit your own job postings",
      });
    }

    // Build update object
    const updateData = {};
    if (jobTitle !== undefined) updateData.jobTitle = jobTitle;
    if (jobDescription !== undefined)
      updateData.jobDescription = jobDescription;
    if (requiredSkills !== undefined) {
      updateData.requiredSkills = Array.isArray(requiredSkills)
        ? requiredSkills
        : [requiredSkills];
    }
    if (location !== undefined) updateData.location = location;
    if (jobType !== undefined) updateData.jobType = jobType;
    if (salaryRange !== undefined) updateData.salaryRange = salaryRange;
    if (companyName !== undefined) updateData.companyName = companyName;
    if (experienceLevel !== undefined)
      updateData.experienceLevel = experienceLevel;
    if (applicationDeadline !== undefined)
      updateData.applicationDeadline = applicationDeadline;
    if (isActive !== undefined) updateData.isActive = isActive;

    // Update the job
    const updatedJob = await Job.findByIdAndUpdate(
      id,
      { $set: updateData },
      {
        new: true,
        runValidators: true,
      }
    ).populate("employer", "name email profile.location");

    if (!updatedJob) {
      return res.status(404).json({
        success: false,
        message: "Job not found",
      });
    }

    res.json({
      success: true,
      message: "Job updated successfully",
      data: {
        job: updatedJob,
      },
    });
  } catch (error) {
    console.error("Update job error:", error);

    if (error.name === "CastError") {
      return res.status(400).json({
        success: false,
        message: "Invalid job ID",
      });
    }

    if (error.name === "ValidationError") {
      const validationErrors = Object.values(error.errors).map((err) => ({
        field: err.path,
        message: err.message,
      }));

      return res.status(400).json({
        success: false,
        message: "Validation failed",
        errors: validationErrors,
      });
    }

    res.status(500).json({
      success: false,
      message: "Server error while updating job",
    });
  }
};

module.exports = {
  createJob,
  getAllJobs,
  getJobById,
  getMyJobs,
  updateJob,
};
