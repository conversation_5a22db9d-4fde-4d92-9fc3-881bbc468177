const swaggerJsdoc = require("swagger-jsdoc");
const swaggerUi = require("swagger-ui-express");

const options = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "JobPortal Backend API",
      version: "1.0.0",
      description:
        "A comprehensive job portal API with authentication and job management features",
      contact: {
        name: "JobPortal Team",
        email: "<EMAIL>",
      },
      license: {
        name: "ISC",
        url: "https://opensource.org/licenses/ISC",
      },
    },
    servers: [
      {
        url: "http://localhost:5000",
        description: "Development server",
      },
      {
        url: "https://api.jobportal.com",
        description: "Production server",
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT",
          description: "Enter JWT token",
        },
      },
      schemas: {
        User: {
          type: "object",
          properties: {
            _id: {
              type: "string",
              description: "User ID",
            },
            userType: {
              type: "string",
              enum: ["jobseeker", "employer", "admin"],
              description: "Type of user",
            },
            name: {
              type: "string",
              description: "User full name",
            },
            email: {
              type: "string",
              format: "email",
              description: "User email address",
            },
            phone: {
              type: "string",
              description: "User phone number",
            },
            isActive: {
              type: "boolean",
              description: "Whether user account is active",
            },
            profile: {
              type: "object",
              properties: {
                avatar: {
                  type: "string",
                  description: "Profile picture URL",
                },
                location: {
                  type: "string",
                  description: "User location",
                },
                prefLanguage: {
                  type: "string",
                  description: "Preferred language",
                },
                experienceLevel: {
                  type: "string",
                  enum: ["Entry", "Mid", "Senior", "Executive"],
                  description: "Experience level",
                },
                currentPosition: {
                  type: "string",
                  description: "Current job position",
                },
                education: {
                  type: "string",
                  description: "Educational background",
                },
                skills: {
                  type: "array",
                  items: {
                    type: "string",
                  },
                  description: "List of skills",
                },
              },
            },
            createdAt: {
              type: "string",
              format: "date-time",
              description: "Account creation date",
            },
            updatedAt: {
              type: "string",
              format: "date-time",
              description: "Last update date",
            },
          },
        },
        Job: {
          type: "object",
          properties: {
            _id: {
              type: "string",
              description: "Job ID",
            },
            jobTitle: {
              type: "string",
              description: "Job title",
            },
            jobDescription: {
              type: "string",
              description: "Detailed job description",
            },
            requiredSkills: {
              type: "array",
              items: {
                type: "string",
              },
              description: "Required skills for the job",
            },
            location: {
              type: "string",
              description: "Job location",
            },
            jobType: {
              type: "string",
              enum: [
                "FullTime",
                "PartTime",
                "Contract",
                "Internship",
                "Remote",
              ],
              description: "Type of employment",
            },
            salaryRange: {
              type: "object",
              properties: {
                min: {
                  type: "number",
                  description: "Minimum salary",
                },
                max: {
                  type: "number",
                  description: "Maximum salary",
                },
                currency: {
                  type: "string",
                  enum: ["USD", "EUR", "GBP", "INR", "CNY"],
                  description: "Currency code",
                },
              },
            },
            employer: {
              type: "string",
              description: "Employer user ID",
            },
            companyName: {
              type: "string",
              description: "Company name",
            },
            experienceLevel: {
              type: "string",
              enum: ["Entry", "Mid", "Senior", "Executive"],
              description: "Required experience level",
            },
            applicationDeadline: {
              type: "string",
              format: "date-time",
              description: "Application deadline",
            },
            isActive: {
              type: "boolean",
              description: "Whether job posting is active",
            },
            applicationsCount: {
              type: "number",
              description: "Number of applications received",
            },
            viewsCount: {
              type: "number",
              description: "Number of views",
            },
            createdAt: {
              type: "string",
              format: "date-time",
              description: "Job posting creation date",
            },
            updatedAt: {
              type: "string",
              format: "date-time",
              description: "Last update date",
            },
          },
        },
        Error: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: false,
            },
            message: {
              type: "string",
              description: "Error message",
            },
            errors: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  field: {
                    type: "string",
                  },
                  message: {
                    type: "string",
                  },
                },
              },
              description: "Validation errors",
            },
          },
        },
        Success: {
          type: "object",
          properties: {
            success: {
              type: "boolean",
              example: true,
            },
            message: {
              type: "string",
              description: "Success message",
            },
            data: {
              type: "object",
              description: "Response data",
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
  },
  apis: [
    "./routes/*.js",
    "./controllers/*.js",
    "./server.js",
    "./swagger-docs.js",
  ],
};

const specs = swaggerJsdoc(options);

module.exports = {
  swaggerUi,
  specs,
};
