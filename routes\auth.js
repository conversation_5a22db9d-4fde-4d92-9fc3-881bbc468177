const express = require("express");
const { body } = require("express-validator");
const {
  register,
  login,
  wechatAuth,
  getProfile,
} = require("../controllers/authController");
const { authenticateToken } = require("../middleware/authMiddleware");

const router = express.Router();
// Routes
// POST /api/auth/register
router.post("/register", register);

// POST /api/auth/login
router.post("/login", login);

// POST /api/auth/wechat
router.post("/wechat", wechatAuth);

// GET /api/auth/profile - Protected route
router.get("/profile", authenticateToken, getProfile);

module.exports = router;
