const express = require("express");
const { body } = require("express-validator");
const {
  register,
  login,
  wechatAuth,
  getProfile,
  updateProfile,
} = require("../controllers/authController");
const { authenticateToken } = require("../middleware/authMiddleware");

const router = express.Router();

// Validation rules
const registerValidation = [
  body("userType")
    .isIn(["jobseeker", "employer", "admin"])
    .withMessage("User type must be jobseeker, employer, or admin"),
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Name must be between 2 and 100 characters"),
  body("email")
    .isEmail()
    .normalizeEmail()
    .withMessage("Please provide a valid email"),
  body("phone")
    .matches(/^[0-9]{10,15}$/)
    .withMessage("Phone number must be 10-15 digits"),
  body("password")
    .isLength({ min: 6 })
    .withMessage("Password must be at least 6 characters long")
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      "Password must contain at least one lowercase letter, one uppercase letter, and one number"
    ),
];

const loginValidation = [
  body("phone")
    .matches(/^[0-9]{10,15}$/)
    .withMessage("Phone number must be 10-15 digits"),
  body("password").notEmpty().withMessage("Password is required"),
];

const wechatValidation = [
  body("wechatToken").notEmpty().withMessage("WeChat token is required"),
];

const updateProfileValidation = [
  body("name")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Name must be between 2 and 100 characters"),
  body("email")
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage("Please provide a valid email"),
  body("phone")
    .optional()
    .matches(/^[0-9]{10,15}$/)
    .withMessage("Phone number must be 10-15 digits"),
  body("profile.avatar")
    .optional()
    .isURL()
    .withMessage("Avatar must be a valid URL"),
  body("profile.location")
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage("Location cannot exceed 100 characters"),
  body("profile.prefLanguage")
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage("Preferred language cannot exceed 50 characters"),
  body("profile.experienceLevel")
    .optional()
    .isIn(["Entry", "Mid", "Senior", "Executive"])
    .withMessage("Experience level must be Entry, Mid, Senior, or Executive"),
  body("profile.currentPosition")
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage("Current position cannot exceed 100 characters"),
  body("profile.education")
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage("Education cannot exceed 200 characters"),
  body("profile.skills")
    .optional()
    .isArray()
    .withMessage("Skills must be an array")
    .custom((skills) => {
      if (
        skills.some(
          (skill) => typeof skill !== "string" || skill.trim().length === 0
        )
      ) {
        throw new Error("All skills must be non-empty strings");
      }
      if (skills.length > 20) {
        throw new Error("Cannot have more than 20 skills");
      }
      return true;
    }),
];

// Routes
// POST /api/auth/register
router.post("/register", registerValidation, register);

// POST /api/auth/login
router.post("/login", loginValidation, login);

// POST /api/auth/wechat
router.post("/wechat", wechatValidation, wechatAuth);

// GET /api/auth/profile - Protected route
router.get("/profile", authenticateToken, getProfile);

// PUT /api/auth/profile - Update profile (Protected route)
router.put(
  "/profile",
  authenticateToken,
  updateProfileValidation,
  updateProfile
);

module.exports = router;
