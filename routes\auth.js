const express = require("express");
const { body } = require("express-validator");
const {
  register,
  login,
  wechatAuth,
  getProfile,
  updateProfile,
  uploadResume,
  deleteResume,
} = require("../controllers/authController");
const {
  authenticateToken,
  authorizeRoles,
} = require("../middleware/authMiddleware");
const {
  uploadResume: uploadMiddleware,
  handleUploadError,
} = require("../middleware/uploadMiddleware");

const router = express.Router();

// Validation rules
const registerValidation = [
  body("userType")
    .isIn(["jobseeker", "employer", "admin"])
    .withMessage("User type must be jobseeker, employer, or admin"),
  body("name")
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Name must be between 2 and 100 characters"),
  body("email")
    .isEmail()
    .normalizeEmail()
    .withMessage("Please provide a valid email"),
  body("phone")
    .matches(/^[0-9]{10,15}$/)
    .withMessage("Phone number must be 10-15 digits"),
  body("password")
    .isLength({ min: 6 })
    .withMessage("Password must be at least 6 characters long")
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage(
      "Password must contain at least one lowercase letter, one uppercase letter, and one number"
    ),
];

const loginValidation = [
  body("phone")
    .matches(/^[0-9]{10,15}$/)
    .withMessage("Phone number must be 10-15 digits"),
  body("password").notEmpty().withMessage("Password is required"),
];

const wechatValidation = [
  body("wechatToken").notEmpty().withMessage("WeChat token is required"),
];

const updateProfileValidation = [
  body("name")
    .optional()
    .trim()
    .isLength({ min: 2, max: 100 })
    .withMessage("Name must be between 2 and 100 characters"),
  body("email")
    .optional()
    .isEmail()
    .normalizeEmail()
    .withMessage("Please provide a valid email"),
  body("phone")
    .optional()
    .matches(/^[0-9]{10,15}$/)
    .withMessage("Phone number must be 10-15 digits"),
  body("profile.avatar")
    .optional()
    .isURL()
    .withMessage("Avatar must be a valid URL"),
  body("profile.location")
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage("Location cannot exceed 100 characters"),
  body("profile.prefLanguage")
    .optional()
    .trim()
    .isLength({ max: 50 })
    .withMessage("Preferred language cannot exceed 50 characters"),
  body("profile.experienceLevel")
    .optional()
    .isIn(["Entry", "Mid", "Senior", "Executive"])
    .withMessage("Experience level must be Entry, Mid, Senior, or Executive"),
  body("profile.currentPosition")
    .optional()
    .trim()
    .isLength({ max: 100 })
    .withMessage("Current position cannot exceed 100 characters"),
  body("profile.education")
    .optional()
    .trim()
    .isLength({ max: 200 })
    .withMessage("Education cannot exceed 200 characters"),
  body("profile.skills")
    .optional()
    .isArray()
    .withMessage("Skills must be an array")
    .custom((skills) => {
      if (
        skills.some(
          (skill) => typeof skill !== "string" || skill.trim().length === 0
        )
      ) {
        throw new Error("All skills must be non-empty strings");
      }
      if (skills.length > 20) {
        throw new Error("Cannot have more than 20 skills");
      }
      return true;
    }),
];

// Routes

/**
 * @swagger
 * /api/auth/register:
 *   post:
 *     summary: Register a new user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - userType
 *               - name
 *               - email
 *               - phone
 *               - password
 *             properties:
 *               userType:
 *                 type: string
 *                 enum: [jobseeker, employer, admin]
 *                 description: Type of user account
 *                 example: jobseeker
 *               name:
 *                 type: string
 *                 minLength: 2
 *                 maxLength: 100
 *                 description: Full name of the user
 *                 example: John Doe
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address
 *                 example: <EMAIL>
 *               phone:
 *                 type: string
 *                 pattern: '^[0-9]{10,15}$'
 *                 description: Phone number (10-15 digits)
 *                 example: "**********"
 *               password:
 *                 type: string
 *                 minLength: 6
 *                 description: Password (must contain uppercase, lowercase, and number)
 *                 example: Password123
 *     responses:
 *       201:
 *         description: User registered successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: User registered successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *                     token:
 *                       type: string
 *                       description: JWT authentication token
 *       400:
 *         description: Validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       409:
 *         description: User already exists
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/register", registerValidation, register);

/**
 * @swagger
 * /api/auth/login:
 *   post:
 *     summary: Login user
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - phone
 *               - password
 *             properties:
 *               phone:
 *                 type: string
 *                 pattern: '^[0-9]{10,15}$'
 *                 description: Phone number
 *                 example: "**********"
 *               password:
 *                 type: string
 *                 description: User password
 *                 example: Password123
 *     responses:
 *       200:
 *         description: Login successful
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Login successful
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *                     token:
 *                       type: string
 *                       description: JWT authentication token
 *       400:
 *         description: Validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Invalid credentials or account deactivated
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/login", loginValidation, login);

/**
 * @swagger
 * /api/auth/wechat:
 *   post:
 *     summary: WeChat authentication
 *     tags: [Authentication]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - wechatToken
 *             properties:
 *               wechatToken:
 *                 type: string
 *                 description: WeChat authentication token
 *                 example: "wechat_token_here"
 *     responses:
 *       200:
 *         description: WeChat authentication successful
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Invalid WeChat token
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post("/wechat", wechatValidation, wechatAuth);

/**
 * @swagger
 * /api/auth/profile:
 *   get:
 *     summary: Get current user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Authentication required
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get("/profile", authenticateToken, getProfile);

/**
 * @swagger
 * /api/auth/profile:
 *   put:
 *     summary: Update user profile
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               name:
 *                 type: string
 *                 example: John Updated
 *               email:
 *                 type: string
 *                 format: email
 *                 example: <EMAIL>
 *               phone:
 *                 type: string
 *                 example: "9876543210"
 *               profile:
 *                 type: object
 *                 properties:
 *                   avatar:
 *                     type: string
 *                     example: "https://example.com/avatar.jpg"
 *                   location:
 *                     type: string
 *                     example: "New York, NY"
 *                   skills:
 *                     type: array
 *                     items:
 *                       type: string
 *                     example: ["JavaScript", "React", "Node.js"]
 *     responses:
 *       200:
 *         description: Profile updated successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       400:
 *         description: Validation failed
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.put(
  "/profile",
  authenticateToken,
  updateProfileValidation,
  updateProfile
);

/**
 * @swagger
 * /api/auth/upload-resume:
 *   post:
 *     summary: Upload resume (Job seekers only)
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         multipart/form-data:
 *           schema:
 *             type: object
 *             required:
 *               - resume
 *             properties:
 *               resume:
 *                 type: string
 *                 format: binary
 *                 description: Resume file (PDF, DOC, or DOCX, max 5MB)
 *     responses:
 *       200:
 *         description: Resume uploaded successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Resume uploaded successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     user:
 *                       $ref: '#/components/schemas/User'
 *                     resume:
 *                       type: object
 *                       properties:
 *                         filename:
 *                           type: string
 *                         originalName:
 *                           type: string
 *                         fileSize:
 *                           type: number
 *                         fileUrl:
 *                           type: string
 *       400:
 *         description: No file uploaded or invalid file type
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Only job seekers can upload resumes
 *       500:
 *         description: Server error
 */
router.post(
  "/upload-resume",
  authenticateToken,
  authorizeRoles("jobseeker"),
  uploadMiddleware,
  handleUploadError,
  uploadResume
);

/**
 * @swagger
 * /api/auth/delete-resume:
 *   delete:
 *     summary: Delete resume (Job seekers only)
 *     tags: [Authentication]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Resume deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Success'
 *       401:
 *         description: Authentication required
 *       403:
 *         description: Only job seekers can delete resumes
 *       404:
 *         description: No resume found to delete
 *       500:
 *         description: Server error
 */
router.delete(
  "/delete-resume",
  authenticateToken,
  authorizeRoles("jobseeker"),
  deleteResume
);

module.exports = router;
